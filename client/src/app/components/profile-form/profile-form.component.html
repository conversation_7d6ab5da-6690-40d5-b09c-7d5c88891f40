

<form *ngIf="profileService.profile && (isProfile || (!isProfile && !questionnaireSentState))" class="profile-form {{!isProfile ? 'pt-[120px]' : ''}}" [formGroup]="form">
  <div class="form-group" [ngClass]="{'active-form': selectedMobileForm === formsSectionType.BASIC_INFORMATION}">
    <div class="form-group__title">Основная информация</div>
    <div class="form-group__fields">
      <div class="form-group__field">
        <label>Имя</label>
        <div class="input-wrap">
          
          <input formControlName="firstName" type="text">
        </div>
      </div>
      <div class="form-group__field">
        <label>Фамилия</label>
        <div class="input-wrap">
          
          <input formControlName="lastName" type="text">
        </div>
      </div>
      <div class="form-group__field">
        <label>Отчество</label>
        <div class="input-wrap">
          
          <input formControlName="middleName" type="text">
        </div>
      </div>
      <div class="form-group__field">
        <label>E-mail *</label>
        <div class="input-wrap">
          
          <input formControlName="email" type="text">
        </div>
      </div>
      <div class="form-group__field">
        <label>Духовное имя (если есть)</label>
        <div class="input-wrap">
          
          <input formControlName="spiritualName" type="text">
        </div>
      </div>
      <div class="form-group__field">
        <label>Духовный статус</label>
        <div class="select-wrapper" *ngIf="statuses" (mouseleave)="dropdownOpenStates['statuses']=false">
          <button class="dropdown-btn" (click)="toggleDrpd('statuses')">
            <span class="txt_hd">{{ dropdownSelectedValues['statuses'] }}</span>
          </button>
          @if (dropdownOpenStates['statuses']) {
            <div class="dropdown-content">
              <div class="dropdown-content-inner">
                @for(option of statuses; track option.value) {
                <div (click)="selectDrpdValue(option.value, option.label, 'statuses')" class="dropdown-item"
                  [class.active]="form.get('statuses')?.value === option.value">
                  {{ option.label }}
                </div>
                }
              </div>
            </div>
          }
        </div>
        <!-- <select formControlName="statuses" *ngIf="statuses" multiple>
          @for(status of statuses; track status.value) {
            <option [value]="status.value">{{status.label}}</option>
          }
        </select> -->
      </div>
      <div class="form-group__field">
        <label>Дата рождения</label>
        <div class="input-wrap date-input-wrap">
          
          <!-- <input formControlName="birthDate" type="date"> -->
          <input formControlName="birthDate" type="date" class="custom-date-input">
          <div class="custom-calendar-icon">
            <svg width="21" height="22" viewBox="0 0 21 22" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20.5212 9.47351C20.5212 9.57877 20.5212 9.66429 20.5212 9.75639C20.5212 12.4207 20.5212 15.0916 20.5212 17.756C20.5212 19.3151 19.5903 20.5716 18.1716 20.9203C17.9084 20.9861 17.6324 21.0058 17.3627 21.0058C12.6379 21.0124 7.90669 21.0124 3.18187 21.0058C1.56414 21.0058 0.331578 19.9467 0.0555354 18.3481C0.0234375 18.1704 0.0234375 17.9862 0.0234375 17.8086C0.0234375 15.1114 0.0234375 12.4076 0.0234375 9.71034C0.0234375 9.63798 0.0298571 9.56561 0.0362767 9.48009C6.85388 9.47351 13.6651 9.47351 20.5212 9.47351Z" fill="var(--text-color)"/>
              <path d="M16.4187 3.15144C16.7654 3.15144 17.08 3.15144 17.3881 3.15144C19.1856 3.16459 20.5016 4.51321 20.5144 6.3618C20.5144 6.67757 20.5144 6.99992 20.5144 7.33543C13.6776 7.33543 6.85998 7.33543 0.035957 7.33543C-0.0795956 6.03287 0.035957 4.81582 1.09519 3.9014C1.61518 3.45405 2.20578 3.19091 2.88625 3.16459C3.28427 3.14486 3.68228 3.16459 4.11239 3.16459C4.11239 2.79619 4.11239 2.4541 4.11239 2.11202C4.11239 1.77651 4.11239 1.441 4.11239 1.10549C4.11881 0.467364 4.54251 0.0134399 5.11385 0.000282732C5.71087 -0.0128745 6.15382 0.434471 6.16666 1.0726C6.1795 1.66467 6.16666 2.25675 6.16666 2.84224C6.16666 2.93434 6.16666 3.01986 6.16666 3.1317C8.9014 3.1317 11.6169 3.1317 14.3645 3.1317C14.3645 2.93434 14.3645 2.75014 14.3645 2.56594C14.3645 2.07254 14.3581 1.57915 14.3645 1.08575C14.3773 0.447628 14.8074 0.000282732 15.398 0.000282732C15.9822 0.00686134 16.4123 0.467364 16.4123 1.10549C16.4252 1.77651 16.4187 2.44753 16.4187 3.15144Z" fill="var(--text-color)"/>
            </svg>    
          </div>
        </div>
      </div>
      <div class="form-group__field">
        <label>Страна</label>
        <div class="select-wrapper" (mouseleave)="dropdownOpenStates['country']=false">
          <button class="dropdown-btn" (click)="toggleDrpd('country')">
            <span class="txt_hd">{{ dropdownSelectedValues['country'] }}</span>
          </button>
          @if (dropdownOpenStates['country']) {
            <div class="dropdown-content">
              <div class="dropdown-content-inner">
                @for(option of countries; track option.iso_code2) {
                <div (click)="selectDrpdValue(option.iso_code2, option.name_ru, 'country')" class="dropdown-item"
                  [class.active]="form.get('country')?.value === option.iso_code2">
                  
                  {{ option.name_ru }} 
                </div>
                }
              </div>
            </div>
          }
        </div>
        <!-- <select class="form-select" formControlName="country">
          <option value="">Выберите страну</option>
          @for(country of countries; track country) {
            <option [value]="country.iso_code2">{{country.name_ru}}</option>
          }
        </select> -->
      </div>
      <div class="form-group__field">
        <label>Город</label>
        <div class="input-wrap">
          
          <input formControlName="city" type="text">
        </div>
      </div>
      <!-- <div class="form-group__field">
        <label>Адрес</label>
        <div class="input-wrap">
          
          <input formControlName="address" type="text">
        </div>
      </div> -->
      <div class="form-group__field">
        <label>Телефон</label>
        <div class="input-wrap">
          
          <input formControlName="phone" type="text">
        </div>
      </div>
      <div class="form-group__field">
        <label>Телеграм</label>
        <div class="input-wrap">
          
          <input formControlName="telegram" type="text">
        </div>
      </div>
      <!-- <div class="form-group__field">
        <label>Язык общения</label>
        <div class="input-wrap">
          
          <input formControlName="language" type="text">
        </div>
      </div> -->
    </div>
    <div class="forms-nav-buttons">
      <button class="forms-nav-button" (click)="goToNextForm()">
        <img class="btn-backdrop-img" src="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
        <div class="btn-label">Далее</div>
      </button>
    </div>
  </div>
  <div class="divider">
  </div>
  <div class="form-group form-wide spiritual-path-form" [ngClass]="{'active-form': selectedMobileForm === formsSectionType.SPIRITUAL_PATH}">
    <div class="form-group__title">Духовный путь</div>
    <div class="form-group__fields">
      <div class="form-group__field">
        <label>Дата принятия Символа веры / Прибежища</label>
        <div class="input-wrap date-input-wrap">
        
          <input formControlName="dpDate" type="date" class="custom-date-input">
          <div class="custom-calendar-icon">
            <svg width="21" height="22" viewBox="0 0 21 22" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20.5212 9.47351C20.5212 9.57877 20.5212 9.66429 20.5212 9.75639C20.5212 12.4207 20.5212 15.0916 20.5212 17.756C20.5212 19.3151 19.5903 20.5716 18.1716 20.9203C17.9084 20.9861 17.6324 21.0058 17.3627 21.0058C12.6379 21.0124 7.90669 21.0124 3.18187 21.0058C1.56414 21.0058 0.331578 19.9467 0.0555354 18.3481C0.0234375 18.1704 0.0234375 17.9862 0.0234375 17.8086C0.0234375 15.1114 0.0234375 12.4076 0.0234375 9.71034C0.0234375 9.63798 0.0298571 9.56561 0.0362767 9.48009C6.85388 9.47351 13.6651 9.47351 20.5212 9.47351Z" fill="var(--text-color)"/>
              <path d="M16.4187 3.15144C16.7654 3.15144 17.08 3.15144 17.3881 3.15144C19.1856 3.16459 20.5016 4.51321 20.5144 6.3618C20.5144 6.67757 20.5144 6.99992 20.5144 7.33543C13.6776 7.33543 6.85998 7.33543 0.035957 7.33543C-0.0795956 6.03287 0.035957 4.81582 1.09519 3.9014C1.61518 3.45405 2.20578 3.19091 2.88625 3.16459C3.28427 3.14486 3.68228 3.16459 4.11239 3.16459C4.11239 2.79619 4.11239 2.4541 4.11239 2.11202C4.11239 1.77651 4.11239 1.441 4.11239 1.10549C4.11881 0.467364 4.54251 0.0134399 5.11385 0.000282732C5.71087 -0.0128745 6.15382 0.434471 6.16666 1.0726C6.1795 1.66467 6.16666 2.25675 6.16666 2.84224C6.16666 2.93434 6.16666 3.01986 6.16666 3.1317C8.9014 3.1317 11.6169 3.1317 14.3645 3.1317C14.3645 2.93434 14.3645 2.75014 14.3645 2.56594C14.3645 2.07254 14.3581 1.57915 14.3645 1.08575C14.3773 0.447628 14.8074 0.000282732 15.398 0.000282732C15.9822 0.00686134 16.4123 0.467364 16.4123 1.10549C16.4252 1.77651 16.4187 2.44753 16.4187 3.15144Z" fill="var(--text-color)"/>
            </svg>    
          </div>
        </div>
      </div>
      <div class="form-group__field">
        <label>Цели практики</label>

        <div class="select-wrapper" (mouseleave)="dropdownOpenStates['dpGoal']=false">
          <button class="dropdown-btn" (click)="toggleDrpd('dpGoal')">
            <span class="txt_hd">{{ dropdownSelectedValues['dpGoal'] }}</span>
          </button>
          @if (dropdownOpenStates['dpGoal']) {
            <div class="dropdown-content">
              <div class="dropdown-content-inner">
                @for(option of goalsPractice; track option) {
                  <div (click)="selectDrpdValue(option, option, 'dpGoal')" class="dropdown-item"
                    [class.active]="form.get('dpGoal')?.value === option">
                    {{ option }}
                  </div>
                }
              </div>
            </div>
          }
        </div>
        <!-- <select formControlName="dpGoal">
          <option selected value="Гость (познакомиться с традицией)">Гость (познакомиться с традицией)</option>
          <option value="Принятие через вашу школу индуизма как своей религии">Принятие через вашу школу индуизма как своей религии</option>
          <option value="Грихастха (стать учеником Гуру с минимальным количеством обетов)">Грихастха (стать учеником Гуру с минимальным количеством обетов)</option>
          <option value="Карма-санньяса (стать учеником Гуру с более строгими обетами)">Карма-санньяса (стать учеником Гуру с более строгими обетами)</option>
          <option value="Пурна-санньяса (стать монахом)">Пурна-санньяса (стать монахом)</option>
          <option value="Ванапрастха (статус отшельников, принимаемый после 50 лет с супругой или одиночно)">Ванапрастха (статус отшельников, принимаемый после 50 лет с супругой или одиночно)</option>
        </select> -->
      </div>

      <div class="form-group__field">
        <label>Как вы оцениваете свое здоровье?</label>
        <div class="input-wrap">
          <textarea formControlName="dpHealth"></textarea>
        </div>
      </div>

      <div class="form-group__field">
        <label>Практиковали ли в другой школе, традиции, под чьим то руководством?</label>
        <div class="input-wrap">
        
          <textarea formControlName="dpPractice"></textarea>
        </div>
      </div>
      <div class="form-group__field">
        <label>Как оцениваете свой уровень практики? Были ли духовные, мистические опыты?</label>
      <div class="input-wrap">
      
        <textarea formControlName="dpLevel"></textarea>
      </div>
      </div>
      <div class="form-group__field">
        <label>Принимали ли ранее участие в мероприятиях нашей общины?</label>
        <div class="input-wrap">
        
          <textarea formControlName="dpEvents"></textarea>
        </div>
      </div>
      <div class="form-group__field">
        <label>Как вы познакомились с нашей общиной?</label>
        <div class="input-wrap">
        
          <textarea formControlName="dpMeet"></textarea>
        </div>
      </div>

    </div>
    <div class="forms-nav-buttons">
      <button class="forms-nav-button" (click)="goToNextForm()">
        <img class="btn-backdrop-img" src="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
        <div class="btn-label">Далее</div>
      </button>
      <button class="forms-nav-button" (click)="goToPrevForm()">
        <img class="btn-backdrop-img" src="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
        <div class="btn-label">Назад</div>
      </button>
    </div>
  </div>
  <div class="divider">
  </div>
  <div class="form-group" [ngClass]="{'active-form': selectedMobileForm === formsSectionType.EDUCATION}">
    <div class="form-group__title">Образование, навыки и служение</div>
    <div class="form-group__fields">
      <div class="form-group__field">
        <label>Образование</label>
        <div class="select-wrapper" (mouseleave)="dropdownOpenStates['education']=false">
          <button class="dropdown-btn" (click)="toggleDrpd('education')">
            <span class="txt_hd">{{ dropdownSelectedValues['education'] }}</span>
          </button>
          @if (dropdownOpenStates['education']) {
            <div class="dropdown-content">
              <div class="dropdown-content-inner">
                @for(option of educations; track option) {
                <div (click)="selectDrpdValue(option, option, 'education')" class="dropdown-item"
                  [class.active]="form.get('education')?.value === option">
                  {{ option }}
                </div>
                }
              </div>
            </div>
          }
        </div>
        <!-- <select formControlName="education">
          <option value="не выбрано">не выбрано</option>
          <option value="среднее">среднее</option>
          <option value="среднее-специальное">среднее-специальное</option>
          <option value="неоконченное высшее">неоконченное высшее</option>
          <option value="высшее">высшее</option>
        </select> -->
      </div>
      <div class="form-group__field">
        <label>Специальность</label>
        <div class="input-wrap">
          <input formControlName="speciality" type="text">
        </div>
      </div>
      <div class="form-group__field">
        <label>Профессия</label>
        <div class="input-wrap">
          <input formControlName="profession" type="text">
        </div>
      </div>
      <div class="form-group__field">
        <label>Навыки</label>
        <div class="input-wrap">
          <input formControlName="skills" type="text">
        </div>
      </div>
      <div class="form-group__field">
        <label>Служение</label>
        <div class="input-wrap">
          <input formControlName="service" type="text">
        </div>
      </div>
    </div>
    <div class="forms-nav-buttons">
      <button class="forms-nav-button" (click)="goToNextForm()">
        <img class="btn-backdrop-img" src="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
        <div class="btn-label">Далее</div>
      </button>
      <button class="forms-nav-button" (click)="goToPrevForm()">
        <img class="btn-backdrop-img" src="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
        <div class="btn-label">Назад</div>
      </button>
    </div>
  </div>
  <div class="divider">
  </div>
  <!-- <div class="form-group">
    <div class="form-group__title">Здоровье</div>
    <div class="form-group__fields">
      <div class="form-group__field">
        <label>Состояние здоровья</label>
        <select formControlName="health">
          <option selected value="Совершенно здоров">Совершенно здоров</option>
          <option value="Относительно здоров">Относительно здоров</option>
          <option value="Есть ограничения на физические нагрузки">Есть ограничения на физические нагрузки</option>
          <option value="Есть хронические заболевания">Есть хронические заболевания</option>
          <option value="Есть психиатрические заболевания">Есть психиатрические заболевания</option>
          <option value="Есть зависимость (алкогольная, наркотическая)">Есть зависимость (алкогольная, наркотическая)</option>
        </select>
      </div>
    </div>
  </div> -->
  <div class="form-group feedback-form" [ngClass]="{'active-form': selectedMobileForm === formsSectionType.FEEDBACK}">
    <div class="form-group__title">Обратная связь и поддержка</div>
    <div class="form-group__fields">
      <div class="form-group__field">
        <label>Подобрать инструктора в ближайшем городе? </label>
        <div class="custom-radio-group">
          <div class="custom-radio">
            <input type="radio" id="supportInstructorYes" formControlName="supportInstructor" value="Да" class="custom-radio-input">
            <label for="supportInstructorYes" class="custom-radio-label">
              <span class="custom-radio-button"></span>
              Да
            </label>
          </div>
          <div class="custom-radio">
            <input type="radio" id="supportInstructorNo" formControlName="supportInstructor" value="Нет" class="custom-radio-input">
            <label for="supportInstructorNo" class="custom-radio-label">
              <span class="custom-radio-button"></span>
              Нет
            </label>
          </div>
        </div>
        <!-- <div class="form-radio">
          <input type="radio" formControlName="supportInstructor" value="Да"> Да
        </div>
        <div class="form-radio">
          <input type="radio" formControlName="supportInstructor" value="Нет"> Нет
        </div> -->
      </div>
      <div class="form-group__field">
        <label>Есть ли необходимость в консультации монаха?</label>
        <!-- <div class="form-radio">
          <input type="radio" formControlName="supportConsultation" value="Да"> Да
        </div>
        <div class="form-radio">
          <input type="radio" formControlName="supportConsultation" value="Нет"> Нет
        </div> -->
        <div class="custom-radio-group">
          <div class="custom-radio">
            <input type="radio" id="supportConsultationYes" formControlName="supportConsultation" value="Да" class="custom-radio-input">
            <label for="supportConsultationYes" class="custom-radio-label">
              <span class="custom-radio-button"></span>
              <span class="radio-value">
                Да
              </span>
            </label>
          </div>
          <div class="custom-radio">
            <input type="radio" id="supportConsultationNo" formControlName="supportConsultation" value="Нет" class="custom-radio-input">
            <label for="supportConsultationNo" class="custom-radio-label">
              <span class="custom-radio-button"></span>
              <span class="radio-value">
                Нет
              </span>
            </label>
          </div>
        </div>
      </div>
      <div class="form-group__field">
        <label>Хотели бы вы поступить на заочное обучение?</label>
        <div class="custom-radio-group">
          <div class="custom-radio">
            <input type="radio" id="supportCorrespondenceYes" formControlName="supportCorrespondence" value="Да" class="custom-radio-input">
            <label for="supportCorrespondenceYes" class="custom-radio-label">
              <span class="custom-radio-button"></span>
              <span class="radio-value">
                Да
              </span>
            </label>
          </div>
          <div class="custom-radio">
            <input type="radio" id="supportCorrespondenceNo" formControlName="supportCorrespondence" value="Нет" class="custom-radio-input">
            <label for="supportCorrespondenceNo" class="custom-radio-label">
              <span class="custom-radio-button"></span>
              <span class="radio-value">
                Нет
              </span>
            </label>
          </div>
        </div>
      </div>
      
    </div>
    <!-- <div style="margin-top: 40px">
      <div class="form-radio">
        <input type="checkbox" formControlName="agree" value="true"> Против обработки и хранения моих персональных данных не возражаю
      </div>
    </div> -->
  </div>
  <div class="flex flex-col justify-center items-center form-footer" [ngClass]="{'active-footer': selectedMobileForm === formsSectionType.FEEDBACK}">
    <div class="processing-personal-data">Регистрируясь на сайте, Вы даете согласие на</div>
    <a class="processing-personal-data-link" href="">обработку персональных данных</a>
    <button type="submit" [disabled]="form.invalid" class="send_button" (click)="saveForm()">
      <img class="btn-backdrop-img" src="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
      <div class="btn-label">Сохранить</div>
    </button>
    <div class="forms-nav-buttons">
      <button class="forms-nav-button" (click)="goToPrevForm()">
        <img class="btn-backdrop-img" src="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
        <div class="btn-label">Назад</div>
      </button>
    </div>
  </div>
</form>

<!-- <ng-container *ngIf="!isProfile && questionnaireSentState"> -->
  <div class="questionnaire-sent-container" *ngIf="!profileService.isAuth">
    <img src="assets/images/anketa_sent_icon.svg" width="212" height="212" alt="sent-icon">
    <div class="questionnaire-sent-title">
      Анкета
    </div>
    <div class="questionnaire-sent-description">
      Для доступа к анкете требуется авторизация.<br>Пожалуйста, войдите в систему, чтобы продолжить заполнение.
    </div>
    <div class="questionnaire-sent-actions">
      <a (click)="toAuthPage()" class="questionnaire-sent-button">
        <img class="btn-backdrop-img" src="assets/images/Button1_1_lg.svg" width="234" height="50" alt="bg">
        <div class="btn-label">Авторизоваться</div>
      </a>
      <!-- <a class="questionnaire-sent-button" [routerLink]="[profileService.name() ? '/ru/profile' : '/ru/signin']">
        <img class="btn-backdrop-img" src="assets/images/Button1_1_lg.svg" width="234" height="50" alt="bg">
        <div class="btn-label">Личный кабинет</div>
      </a> -->
    </div>
  </div>

<!-- </ng-container> -->
